import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { AgentTemplateStatus } from '@modules/agent/constants';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ModelConfigDto } from '../agent-system';

/**
 * DTO cho việc cập nhật agent template
 */
export class UpdateAgentTemplateDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsOptional()
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * C<PERSON>u hình model AI
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Thông tin hồ sơ mẫu
   */
  @ApiPropertyOptional({
    description: 'Thông tin hồ sơ mẫu',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  profile?: ProfileAgent;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái của agent template
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của agent template',
    enum: AgentTemplateStatus,
    example: AgentTemplateStatus.ACTIVE,
  })
  @IsEnum(AgentTemplateStatus)
  @IsOptional()
  status?: AgentTemplateStatus;

  /**
   * Cấu hình chuyển đổi
   */
  @ApiPropertyOptional({
    description: 'Cấu hình chuyển đổi',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  convertConfig?: ConvertConfig;

  /**
   * ID của loại agent
   */
  @ApiPropertyOptional({
    description: 'ID của loại agent',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  typeId?: number;

  /**
   * ID của base model (system model)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (system model)',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  modelBaseId?: string;

  /**
   * Trạng thái có thể bán
   */
  @ApiPropertyOptional({
    description: 'Trạng thái có thể bán',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isForSale?: boolean;
}
