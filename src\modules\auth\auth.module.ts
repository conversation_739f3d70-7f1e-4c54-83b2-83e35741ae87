import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from '../user/user.module';
import { User } from '../user/entities';
import { AuthController } from './controller/auth.controller';
import { AuthService } from './service/auth.service';
import { OAuth2Service } from './service/oauth2.service';
import { RolesGuard } from './guards/roles.guard';
import { JwtUtilService } from './guards/jwt.util';
import { JwtUserGuard } from '@modules/auth/guards';
import { ServicesModule } from '@shared/services/services.module';
import { JwtEmployeeGuard } from './guards/jwt-employee.guard';
import { EmailModule } from '@modules/email/email.module';
import { AuthClientModule } from './client/auth-client.module';
import { HttpModule } from '@nestjs/axios';
import { QueueModule } from '@/shared/queue/queue.module';

@Global()
@Module({
  imports: [
    UserModule,
    ServicesModule,
    EmailModule,
    AuthClientModule,
    HttpModule,
    QueueModule,
    TypeOrmModule.forFeature([User]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRATION_TIME', '1d'),
        },
      }),
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, OAuth2Service, RolesGuard, JwtUtilService, JwtUserGuard, JwtEmployeeGuard],
  exports: [
    AuthService,
    PassportModule,
    RolesGuard,
    JwtEmployeeGuard,
    JwtUtilService,
    JwtModule,
    JwtUserGuard,
  ],
})
export class AuthModule {}
