import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length, Matches } from 'class-validator';

/**
 * DTO cho yêu cầu ký hợp đồng cá nhân với OTP
 */
export class SignIndividualContractDto {
  /**
   * Token OTP từ bước gửi OTP
   */
  @ApiProperty({
    description: 'Token OTP từ bước gửi OTP',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: true,
  })
  @IsNotEmpty({ message: 'Token OTP không được để trống' })
  @IsString({ message: 'Token OTP phải là chuỗi' })
  otpToken: string;

  /**
   * Mã OTP xác thực
   */
  @ApiProperty({
    description: 'Mã OTP xác thực (6 chữ số)',
    example: '123456',
    required: true,
  })
  @IsNotEmpty({ message: 'Mã OTP không được để trống' })
  @IsString({ message: 'Mã OTP phải là chuỗi' })
  @Length(6, 6, { message: 'Mã OTP phải có đúng 6 chữ số' })
  @Matches(/^\d{6}$/, { message: 'Mã OTP chỉ được chứa số' })
  otpCode: string;

  /**
   * Dữ liệu chữ ký tay dạng Base64
   */
  @ApiProperty({
    description: 'Dữ liệu chữ ký tay dạng Base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAA...',
    required: true,
  })
  @IsNotEmpty({ message: 'Dữ liệu chữ ký không được để trống' })
  @IsString({ message: 'Dữ liệu chữ ký phải là chuỗi' })
  signatureBase64: string;
}

/**
 * DTO cho yêu cầu gửi OTP để ký hợp đồng cá nhân
 */
export class SendOtpForSigningDto {
  /**
   * Phương thức gửi OTP (email hoặc sms)
   */
  @ApiProperty({
    description: 'Phương thức gửi OTP',
    example: 'email',
    enum: ['email', 'sms'],
    required: true,
  })
  @IsNotEmpty({ message: 'Phương thức gửi OTP không được để trống' })
  @IsString({ message: 'Phương thức gửi OTP phải là chuỗi' })
  @Matches(/^(email|sms)$/, { message: 'Phương thức gửi OTP phải là email hoặc sms' })
  method: 'email' | 'sms';
}

/**
 * DTO cho phản hồi gửi OTP
 */
export class SendOtpResponseDto {
  /**
   * Token OTP để xác thực
   */
  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  otpToken: string;

  /**
   * Thời gian hết hạn của OTP (giây)
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của OTP (giây)',
    example: 300,
  })
  expiresIn: number;

  /**
   * Thông tin đã được che một phần (email hoặc số điện thoại)
   */
  @ApiProperty({
    description: 'Thông tin đã được che một phần',
    example: 'u***@example.com hoặc 091****678',
  })
  maskedInfo: string;

  /**
   * Mã OTP (chỉ hiển thị trong môi trường phát triển)
   */
  @ApiProperty({
    description: 'Mã OTP (chỉ hiển thị trong môi trường phát triển)',
    example: '123456',
    required: false,
  })
  otp?: string;
}
