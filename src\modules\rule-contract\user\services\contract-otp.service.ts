import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException } from '@common/exceptions';
import { RULE_CONTRACT_ERROR_CODES } from '../../errors';
import { RedisService } from '@/shared/services/redis.service';
import { JwtUtilService } from '@/modules/auth/guards/jwt.util';
import { SendWithTemplateService } from '@/modules/email/services/send-with-template.service';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { SendOtpResponseDto } from '../dto/sign-individual-contract.dto';

/**
 * Service xử lý OTP cho ký hợp đồng
 */
@Injectable()
export class ContractOtpService {
  private readonly logger = new Logger(ContractOtpService.name);
  private readonly contractSigningPrefix = 'contract_signing_otp:';
  private readonly otpExpiryTime = 300; // 5 phút

  constructor(
    private readonly redisService: RedisService,
    private readonly jwtService: JwtUtilService,
    private readonly emailService: SendWithTemplateService,
    private readonly userRepository: UserRepository,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo OTP 6 chữ số
   * @returns OTP string
   */
  private generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Che một phần email
   * @param email Email cần che
   * @returns Email đã che
   */
  private maskEmail(email: string): string {
    const [localPart, domain] = email.split('@');
    if (localPart.length <= 2) {
      return `${localPart[0]}***@${domain}`;
    }
    return `${localPart[0]}***${localPart[localPart.length - 1]}@${domain}`;
  }

  /**
   * Che một phần số điện thoại
   * @param phoneNumber Số điện thoại cần che
   * @returns Số điện thoại đã che
   */
  private maskPhoneNumber(phoneNumber: string): string {
    if (phoneNumber.length <= 6) {
      return `${phoneNumber.substring(0, 3)}***${phoneNumber.substring(phoneNumber.length - 3)}`;
    }
    return `${phoneNumber.substring(0, 3)}****${phoneNumber.substring(phoneNumber.length - 3)}`;
  }

  /**
   * Gửi OTP qua email để ký hợp đồng
   * @param userId ID người dùng
   * @param contractId ID hợp đồng
   * @returns Thông tin OTP response
   */
  async sendOtpForSigning(userId: number, contractId: number): Promise<SendOtpResponseDto> {
    try {
      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.CONTRACT_NOT_FOUND,
          'Không tìm thấy người dùng',
        );
      }

      if (!user.email) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
          'Người dùng chưa có email để nhận OTP',
        );
      }

      // Tạo OTP 6 số
      const otp = this.generateOTP();

      // Tạo token OTP
      const { token: otpToken, expiresInSeconds } = this.jwtService.generateUserVerifyToken({
        sub: userId,
        username: user.email,
      });

      // Lưu thông tin OTP vào Redis
      const verificationData = {
        userId,
        contractId,
        email: user.email,
        otp,
        createdAt: Date.now(),
      };

      const redisKey = `${this.contractSigningPrefix}${otpToken}`;
      await this.redisService.setWithExpiry(
        redisKey,
        JSON.stringify(verificationData),
        this.otpExpiryTime,
      );

      // Gửi email chứa OTP
      try {
        await this.emailService.sendContractSignatureOTP(
          user.email,
          userId,
          otp,
        );
        this.logger.log(`Đã gửi OTP ký hợp đồng đến email ${user.email}`);
      } catch (error) {
        this.logger.error(
          `Lỗi khi gửi email OTP đến ${user.email}: ${error.message}`,
          error.stack,
        );
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.EMAIL_SENDING_FAILED,
          'Không thể gửi email OTP',
        );
      }

      // Che một phần email
      const maskedEmail = this.maskEmail(user.email);

      const response: SendOtpResponseDto = {
        otpToken,
        expiresIn: this.otpExpiryTime,
        maskedInfo: maskedEmail,
      };

      // Trong môi trường phát triển, trả về cả OTP để tiện test
      if (this.configService.get<string>('NODE_ENV') !== 'production') {
        response.otp = otp;
      }

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi gửi OTP ký hợp đồng: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.OTP_GENERATION_FAILED,
        'Lỗi khi tạo OTP ký hợp đồng',
      );
    }
  }

  /**
   * Xác thực OTP cho ký hợp đồng
   * @param otpToken Token OTP
   * @param otpCode Mã OTP
   * @param contractId ID hợp đồng
   * @returns true nếu OTP hợp lệ
   */
  async verifyOtpForSigning(otpToken: string, otpCode: string, contractId: number): Promise<boolean> {
    try {
      // Lấy dữ liệu từ Redis
      const redisKey = `${this.contractSigningPrefix}${otpToken}`;
      const verificationDataStr = await this.redisService.get(redisKey);

      if (!verificationDataStr) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.OTP_EXPIRED,
          'OTP đã hết hạn hoặc không tồn tại',
        );
      }

      const verificationData = JSON.parse(verificationDataStr);

      // Kiểm tra OTP
      if (verificationData.otp !== otpCode) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_OTP,
          'Mã OTP không chính xác',
        );
      }

      // Kiểm tra contract ID
      if (verificationData.contractId !== contractId) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
          'OTP không khớp với hợp đồng',
        );
      }

      // Xóa OTP khỏi Redis sau khi xác thực thành công
      await this.redisService.del(redisKey);

      this.logger.log(`OTP xác thực thành công cho hợp đồng ${contractId}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xác thực OTP: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.OTP_VERIFICATION_FAILED,
        'Lỗi khi xác thực OTP',
      );
    }
  }
}
