import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { RULE_CONTRACT_ERROR_CODES } from '../../errors';
import { S3Service } from '@/shared/services/s3.service';
import { generateS3Key, CategoryFolderEnum } from '@/shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum } from '@/shared/utils/time/time-interval.util';
import { MediaType } from '@utils/file';
import { 
  GetSignatureUploadUrlDto, 
  SignatureUploadUrlResponseDto 
} from '../dto/sign-business-contract.dto';

/**
 * Service xử lý chữ ký hợp đồng
 */
@Injectable()
export class ContractSignatureService {
  private readonly logger = new Logger(ContractSignatureService.name);
  private readonly uploadExpiryTime = 900; // 15 phút

  constructor(
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo URL tạm thời để upload chữ ký doanh nghiệp
   * @param userId ID người dùng
   * @param contractId ID hợp đồng
   * @param dto Thông tin file upload
   * @returns URL upload và key
   */
  async generateSignatureUploadUrl(
    userId: number,
    contractId: number,
    dto: GetSignatureUploadUrlDto,
  ): Promise<SignatureUploadUrlResponseDto> {
    try {
      // Validate file type
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
      if (!allowedTypes.includes(dto.fileType)) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
          'Loại file không được hỗ trợ. Chỉ chấp nhận PNG, JPEG, JPG',
        );
      }

      // Validate file size (max 5MB)
      const maxFileSize = 5 * 1024 * 1024; // 5MB
      if (dto.fileSize && dto.fileSize > maxFileSize) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
          'Kích thước file không được vượt quá 5MB',
        );
      }

      // Tạo key cho file chữ ký
      const fileExtension = this.getFileExtension(dto.fileType);
      const timestamp = Date.now();
      const signatureKey = generateS3Key({
        baseFolder: 'rule-contracts',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        prefix: `signatures/user-${userId}/contract-${contractId}`,
        fileName: `signature-${timestamp}.${fileExtension}`,
        useTimeFolder: false,
      });

      // Tạo presigned URL
      const uploadUrl = await this.s3Service.createPresignedWithID(
        signatureKey,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        dto.fileType as MediaType,
        dto.fileSize || 5 * 1024 * 1024, // 5MB default
      );

      this.logger.log(`Tạo URL upload chữ ký cho user ${userId}, contract ${contractId}`);

      return {
        uploadUrl,
        signatureKey,
        expiresIn: this.uploadExpiryTime,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo URL upload chữ ký: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.SIGNATURE_UPLOAD_FAILED,
        'Lỗi khi tạo URL upload chữ ký',
      );
    }
  }

  /**
   * Kiểm tra file chữ ký đã được upload thành công
   * @param signatureKey Key của file chữ ký
   * @returns true nếu file tồn tại
   */
  async verifySignatureUploaded(signatureKey: string): Promise<boolean> {
    try {
      const exists = await this.s3Service.checkObjectExists(signatureKey);
      if (!exists) {
        throw new AppException(
          RULE_CONTRACT_ERROR_CODES.SIGNATURE_NOT_FOUND,
          'File chữ ký chưa được upload hoặc không tồn tại',
        );
      }
      return true;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra file chữ ký ${signatureKey}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.SIGNATURE_VERIFICATION_FAILED,
        'Lỗi khi kiểm tra file chữ ký',
      );
    }
  }

  /**
   * Lấy URL xem file chữ ký
   * @param signatureKey Key của file chữ ký
   * @returns URL xem file
   */
  async getSignatureViewUrl(signatureKey: string): Promise<string> {
    try {
      return await this.s3Service.getDownloadUrl(
        signatureKey,
        TimeIntervalEnum.ONE_HOUR,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo URL xem chữ ký ${signatureKey}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.SIGNATURE_URL_GENERATION_FAILED,
        'Lỗi khi tạo URL xem chữ ký',
      );
    }
  }

  /**
   * Xóa file chữ ký
   * @param signatureKey Key của file chữ ký
   */
  async deleteSignature(signatureKey: string): Promise<void> {
    try {
      await this.s3Service.deleteFile(signatureKey);
      this.logger.log(`Đã xóa file chữ ký: ${signatureKey}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa file chữ ký ${signatureKey}: ${error.message}`,
        error.stack,
      );
      // Không throw error để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Lấy extension từ MIME type
   * @param mimeType MIME type
   * @returns File extension
   */
  private getFileExtension(mimeType: string): string {
    const mimeToExt: Record<string, string> = {
      'image/png': 'png',
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
    };
    return mimeToExt[mimeType] || 'jpg';
  }

  /**
   * Validate signature base64 data
   * @param signatureBase64 Base64 signature data
   * @returns true if valid
   */
  validateSignatureBase64(signatureBase64: string): boolean {
    try {
      // Kiểm tra format base64
      const base64Regex = /^data:image\/(png|jpeg|jpg);base64,/;
      if (!base64Regex.test(signatureBase64)) {
        return false;
      }

      // Kiểm tra độ dài tối thiểu
      const base64Data = signatureBase64.split(',')[1];
      if (!base64Data || base64Data.length < 100) {
        return false;
      }

      // Thử decode để kiểm tra tính hợp lệ
      Buffer.from(base64Data, 'base64');
      return true;
    } catch (error) {
      this.logger.warn(`Invalid signature base64 data: ${error.message}`);
      return false;
    }
  }

  /**
   * Convert base64 signature to buffer
   * @param signatureBase64 Base64 signature data
   * @returns Buffer
   */
  convertBase64ToBuffer(signatureBase64: string): Buffer {
    try {
      const base64Data = signatureBase64.split(',')[1];
      return Buffer.from(base64Data, 'base64');
    } catch (error) {
      throw new AppException(
        RULE_CONTRACT_ERROR_CODES.INVALID_DATA,
        'Dữ liệu chữ ký không hợp lệ',
      );
    }
  }
}
