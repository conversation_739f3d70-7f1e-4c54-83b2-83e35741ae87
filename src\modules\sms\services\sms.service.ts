import { Injectable, Logger } from '@nestjs/common';
import { QueueService } from '@/shared/queue/queue.service';
import { AppException } from '@/common';
import { AUTH_ERROR_CODE } from '@/modules/auth/errors';

/**
 * Service xử lý các chức năng liên quan đến SMS
 */
@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(private readonly queueService: QueueService) {}

  /**
   * G<PERSON>i SMS OTP xác thực 2FA
   * @param twoFaCode Mã OTP xác thực 2FA
   * @param phone Số điện thoại người nhận
   * @param userId ID người dùng
   * @returns Promise với ID của job đã tạo
   */
  async sendSmsOtpVerify2FA(
    twoFaCode: string,
    phone: string,
    userId: number,
  ): Promise<string | undefined> {
    try {
      this.logger.log(
        `Preparing to send SMS OTP 2FA to phone ${phone} for user ${userId}`,
      );

      // Tạo nội dung SMS
      const message = `Mã xác thực 2FA của bạn là: ${twoFaCode}. Mã có hiệu lực trong 5 phút.`;

      // Đẩy job vào queue SMS system
      const jobId = await this.queueService.sendSmsSystemJob({
        phone,
        message,
        userId,
        type: 'OTP_2FA',
        twoFaCode,
      });

      this.logger.log(
        `SMS OTP 2FA job added to queue with ID: ${jobId} for user ${userId}`,
      );

      return jobId;
    } catch (error) {
      this.logger.error(
        `Failed to send SMS OTP 2FA to ${phone} for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.SMS_SEND_FAILED,
        'Không thể gửi SMS xác thực 2FA',
      );
    }
  }

  /**
   * Gửi SMS thông thường
   * @param phone Số điện thoại người nhận
   * @param message Nội dung tin nhắn
   * @param userId ID người dùng (tùy chọn)
   * @returns Promise với ID của job đã tạo
   */
  async sendSms(
    phone: string,
    message: string,
    userId?: number,
  ): Promise<string | undefined> {
    try {
      this.logger.log(`Preparing to send SMS to phone ${phone}`);

      // Đẩy job vào queue SMS system
      const jobId = await this.queueService.sendSmsSystemJob({
        phone,
        message,
        userId,
        type: 'GENERAL',
      });

      this.logger.log(`SMS job added to queue with ID: ${jobId}`);

      return jobId;
    } catch (error) {
      this.logger.error(
        `Failed to send SMS to ${phone}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AUTH_ERROR_CODE.SMS_SEND_FAILED,
        'Không thể gửi SMS',
      );
    }
  }
}
