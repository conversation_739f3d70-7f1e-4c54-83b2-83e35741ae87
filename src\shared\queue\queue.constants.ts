/**
 * Enum định nghĩa các tên queue trong hệ thống
 * <PERSON><PERSON><PERSON> bảo tính nhất quán khi sử dụng các queue trong ứng dụng
 */
export enum QueueName {
  /**
   * Queue xử lý email
   */
  EMAIL = 'email',

  /**
   * Queue xử lý tin nhắn SMS
   */
  SMS = 'sms',

  /**
   * Queue xử lý thông báo
   */
  NOTIFICATION = 'notification',

  /**
   * Queue xử lý dữ liệu
   */
  DATA_PROCESS = 'data-process',

  SEND_SYSTEM_EMAIL = 'send-system-email',

  /**
   * Queue xử lý crawl URL cho user
   */
  CRAWL_URL = 'crawl-url',

  /**
   * Queue xử lý crawl URL cho admin
   */
  CRAWL_URL_ADMIN = 'crawl-url-admin',

  /**
   * Queue xử lý email hệ thống với agent
   */
  AGENT = 'agent',

  /**
   * Queue xử lý email hệ thống
   */
  EMAIL_SYSTEM = 'email-system',

  /**
   * Queue xử lý email marketing
   */
  EMAIL_MARKETING = 'email-marketing',

  /**
   * Queue xử lý Zalo ZNS
   */
  ZALO_ZNS = 'zalo-zns',
}

/**
 * Enum định nghĩa các tên job trong mỗi queue
 */
export enum EmailJobName {
  /**
   * Job gửi email thông thường
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi email theo mẫu
   */
  SEND_TEMPLATE_EMAIL = 'send-template-email',
}

/**
 * Enum định nghĩa các tên job trong queue SMS
 */
export enum SmsJobName {
  /**
   * Job gửi SMS thông thường
   */
  SEND_SMS = 'send-sms',

  /**
   * Job gửi SMS theo mẫu
   */
  SEND_TEMPLATE_SMS = 'send-template-sms',

  /**
   * Job gửi SMS OTP xác thực 2FA
   */
  SMS_OTP_VERIFY_2FA = 'sms-otp-verify-2fa',
}

/**
 * Enum định nghĩa các tên job trong queue thông báo
 */
export enum NotificationJobName {
  /**
   * Job gửi thông báo
   */
  SEND_NOTIFICATION = 'send-notification',

  /**
   * Job gửi thông báo theo mẫu
   */
  SEND_TEMPLATE_NOTIFICATION = 'send-template-notification',
}

/**
 * Enum định nghĩa các tên job trong queue email system
 */
export enum EmailSystemJobName {
  /**
   * Job gửi email hệ thống theo template
   */
  SEND_TEMPLATE_EMAIL = 'send-template-email',
}

/**
 * Enum định nghĩa các tên job trong queue email marketing
 */
export enum EmailMarketingJobName {
  /**
   * Job gửi email marketing (single email)
   */
  SEND_EMAIL = 'send-email',

  /**
   * Job gửi batch email marketing (multiple emails)
   */
  SEND_BATCH_EMAIL = 'send-batch-email',
}

/**
 * Enum định nghĩa các tên job trong queue Zalo ZNS
 */
export enum ZaloZnsJobName {
  /**
   * Job gửi ZNS đơn lẻ
   */
  SEND_ZNS = 'send-zns',

  /**
   * Job gửi ZNS theo chiến dịch
   */
  SEND_ZNS_CAMPAIGN = 'send-zns-campaign',

  /**
   * Job gửi batch ZNS (multiple messages)
   */
  SEND_BATCH_ZNS = 'send-batch-zns',
}

/**
 * Enum định nghĩa các tên job trong queue crawl URL
 */
export enum CrawlUrlJobName {
  /**
   * Job crawl URL cho user
   */
  CRAWL_URL = 'crawl-url',

  /**
   * Job crawl URL cho admin
   */
  CRAWL_URL_ADMIN = 'crawl-url-admin',
}

/**
 * Các tùy chọn mặc định cho các job
 */
export const DEFAULT_JOB_OPTIONS = {
  attempts: 3, // Số lần thử lại
  backoff: {
    type: 'exponential', // Kiểu backoff khi retry (fixed, exponential)
    delay: 1000, // Thời gian delay giữa các lần retry (ms)
  },
  removeOnComplete: true, // Tự động xóa job sau khi hoàn thành
  removeOnFail: false, // Giữ lại job thất bại để kiểm tra
};

/**
 * Các tùy chọn cho job ưu tiên cao
 */
export const HIGH_PRIORITY_JOB_OPTIONS = {
  ...DEFAULT_JOB_OPTIONS,
  priority: 1, // Ưu tiên cao hơn
  attempts: 5, // Nhiều lần thử lại hơn
};
