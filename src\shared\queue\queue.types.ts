/**
 * <PERSON><PERSON><PERSON> định nghĩa kiểu dữ liệu cho Queue Job
 */

import { SmsTypeEnum } from '@/modules/sms/enums';

/**
 * Interface cho job gửi email thông thường
 */
export interface EmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * Tiêu đề email
   */
  subject: string;

  /**
   * Nội dung email (HTML)
   */
  content: string;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];
  
  /**
   * Địa chỉ email người gửi (optional, mặc định sẽ lấy từ cấu hình)
   */
  from?: string;

  /**
   * Tệp đính kèm (optional)
   */
  attachments?: EmailAttachment[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho tệp đ<PERSON>h kèm trong email
 */
export interface EmailAttachment {
  /**
   * Tên file
   */
  filename: string;
  
  /**
   * Nội dung file (dạng Buffer, Base64 hoặc đường dẫn)
   */
  content?: string | Buffer;
  
  /**
   * Đường dẫn đến file
   */
  path?: string;
  
  /**
   * Loại MIME của file
   */
  contentType?: string;
}

/**
 * Interface cho job gửi email theo mẫu
 */
export interface TemplateEmailJobData {
  /**
   * Địa chỉ email người nhận
   */
  to: string;

  /**
   * ID của mẫu email
   */
  templateId: string;

  /**
   * Dữ liệu được truyền vào mẫu email
   */
  data: Record<string, any>;

  /**
   * CC - Danh sách email nhận bản sao (optional)
   */
  cc?: string[];

  /**
   * BCC - Danh sách email nhận bản sao ẩn (optional)
   */
  bcc?: string[];

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Interface cho job gửi SMS hệ thống
 */
export interface SmsSystemJobData {
  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * Nội dung tin nhắn
   */
  message: string;

  /**
   * ID người dùng (tùy chọn)
   */
  userId?: number;

  /**
   * Loại SMS
   */
  type: SmsTypeEnum;

  /**
   * Mã OTP (chỉ dành cho loại OTP_2FA)
   */
  twoFaCode?: string;

  /**
   * Thời gian job được tạo
   */
  timestamp: number;
}

/**
 * Tùy chọn cho job
 */
export interface JobOptions {
  /**
   * Độ ưu tiên (số càng nhỏ, ưu tiên càng cao)
   */
  priority?: number;

  /**
   * Độ trễ trước khi thực hiện job (ms)
   */
  delay?: number;

  /**
   * Số lần thử lại nếu job thất bại
   */
  attempts?: number;

  /**
   * Cấu hình backoff cho retry
   */
  backoff?: {
    /**
     * Kiểu backoff: 'fixed' hoặc 'exponential'
     */
    type: 'fixed' | 'exponential';
    
    /**
     * Thời gian delay giữa các lần retry (ms)
     */
    delay: number;
  };

  /**
   * Xóa job sau khi hoàn thành
   */
  removeOnComplete?: boolean;

  /**
   * Xóa job nếu thất bại
   */
  removeOnFail?: boolean;
} 